import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { RecipeGeneratorModule } from './recipe-generator/recipe-generator.module';

@Module({
  imports: [
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST ?? 'redis',
        port: parseInt(process.env.REDIS_PORT ?? '6379'),
      },
    }),
    RecipeGeneratorModule,
  ],
})
export class AppModule {}
